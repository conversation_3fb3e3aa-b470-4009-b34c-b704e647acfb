using System;
using Volo.Abp.Application.Dtos;

namespace FbAutoReplyPlatformExpress.Services.Dtos;

public class AutoReplyCampaignDto : FullAuditedEntityDto<Guid>
{
    public Guid FacebookPostId { get; set; }
    public string CampaignName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string PublicReplyMessage { get; set; } = string.Empty;
    public string? PrivateReplyMessage { get; set; }
    public bool IsActive { get; set; }
    public bool SendPublicReply { get; set; }
    public bool SendPrivateReply { get; set; }
    public bool SendLike { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int MaxRepliesPerUser { get; set; }
    public int TotalRepliesSent { get; set; }
    public int PublicRepliesSent { get; set; }
    public int PrivateRepliesSent { get; set; }
    public int LikesSent { get; set; }
    public DateTime? LastReplyAt { get; set; }
    public bool IncludePostLinkInPrivateReply { get; set; } = false;

    // Private Reply Type and Card Reply Data
    public PrivateReplyType PrivateReplyType { get; set; } = PrivateReplyType.TextReply;
    public CardReplyData? CardReplyData { get; set; }

    // Post metadata fields (for in-memory campaigns)
    public string FacebookPostIdString { get; set; } = string.Empty;
    public string FacebookPageIdString { get; set; } = string.Empty;
    public string PostContent { get; set; } = string.Empty;
    public string? PostPermalinkUrl { get; set; }
    public string? PostPictureUrl { get; set; }
    public DateTime PostCreatedTime { get; set; }

    // Additional properties for UI
    public string PostMessage { get; set; } = string.Empty;
    public string PageName { get; set; } = string.Empty;
}

public class CreateAutoReplyCampaignDto
{
    public Guid FacebookPostId { get; set; }
    public string CampaignName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string PublicReplyMessage { get; set; } = string.Empty;
    public string? PrivateReplyMessage { get; set; }
    public bool SendPublicReply { get; set; } = true;
    public bool SendPrivateReply { get; set; } = false;
    public bool SendLike { get; set; } = false;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int MaxRepliesPerUser { get; set; } = 0;
    public bool IncludePostLinkInPrivateReply { get; set; } = false;
    public PrivateReplyType PrivateReplyType { get; set; } = PrivateReplyType.TextReply;
    public CardReplyData? CardReplyData { get; set; }
}

public class UpdateAutoReplyCampaignDto
{
    public string? CampaignName { get; set; }
    public string? Description { get; set; }
    public string? PublicReplyMessage { get; set; }
    public string? PrivateReplyMessage { get; set; }
    public bool? SendPublicReply { get; set; }
    public bool? SendPrivateReply { get; set; }
    public bool? SendLike { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int? MaxRepliesPerUser { get; set; }
    public bool? IncludePostLinkInPrivateReply { get; set; }
    public PrivateReplyType? PrivateReplyType { get; set; }
    public CardReplyData? CardReplyData { get; set; }
}

public class GetCampaignsInput : PagedAndSortedResultRequestDto
{
    public Guid? FacebookPostId { get; set; }
    public string? SearchText { get; set; }
    public bool? IsActive { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}
