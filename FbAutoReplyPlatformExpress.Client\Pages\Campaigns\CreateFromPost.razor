@page "/campaigns/create-from-post"
@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using System.ComponentModel.DataAnnotations
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web
@using Microsoft.JSInterop
@using FbAutoReplyPlatformExpress.Components
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IAutoReplyCampaignService CampaignService
@inject IUiMessageService UiMessage
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<Card>
    <CardHeader>
        <Row>
            <Column ColumnSize="ColumnSize.Is8">
                <h4>
                    <Icon Name="IconName.Add" />
                    Create Auto-Reply Campaign
                </h4>
                <p class="text-muted mb-0">Configure your auto-reply campaign for the selected Facebook post</p>
            </Column>
            <Column ColumnSize="ColumnSize.Is4" Class="text-end">
                <Button Color="Color.Secondary" Clicked="NavigateBack">
                    <Icon Name="IconName.ArrowLeft" />
                    Back to Post Selection
                </Button>
            </Column>
        </Row>
    </CardHeader>
    <CardBody>
        @if (IsLoading)
        {
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading post information...</p>
            </div>
        }
        else if (SelectedPost != null)
        {
            <!-- Selected Post Preview -->
            <Card Class="mb-4">
                <CardHeader>
                    <h6 class="mb-0">Selected Facebook Post</h6>
                </CardHeader>
                <CardBody>
                    <Row>
                        @if (!string.IsNullOrEmpty(SelectedPost.PostPictureUrl))
                        {
                            <Column ColumnSize="ColumnSize.Is3">
                                <img src="@SelectedPost.PostPictureUrl" 
                                alt="Post image" 
                                class="img-fluid rounded" 
                                style="max-height: 150px; width: 100%; object-fit: cover;" />
                            </Column>
                        }
                        <Column ColumnSize="@(string.IsNullOrEmpty(SelectedPost.PostPictureUrl) ? ColumnSize.Is12 : ColumnSize.Is9)">
                            <div class="d-flex align-items-center mb-2">
                                <strong class="text-primary">@SelectedPost.PageName</strong>
                                <small class="text-muted ms-2">@SelectedPost.PostCreatedTime.ToString("MMM dd, yyyy")</small>
                            </div>
                            @if (!string.IsNullOrEmpty(SelectedPost.PostContent))
                            {
                                <p class="mb-2">@SelectedPost.PostContent</p>
                            }
                            @if (!string.IsNullOrEmpty(SelectedPost.PostPermalinkUrl))
                            {
                                <Button Color="Color.Link" Size="Size.Small" Clicked="() => OpenFacebookPostAsync(SelectedPost.PostPermalinkUrl!)">
                                    <Icon Name="IconName.Link" />
                                    View on Facebook
                                </Button>
                            }
                        </Column>
                    </Row>
                </CardBody>
            </Card>

            <!-- Campaign Form -->
            <Validations @ref="ValidationsRef" Model="@Campaign" ValidateOnLoad="false">
                <Row>
                    <Column ColumnSize="ColumnSize.Is12">
                        <h6>Campaign Configuration</h6>
                        <hr />
                    </Column>
                </Row>

                <Row>
                    <Column ColumnSize="ColumnSize.Is12">
                        <Validation>
                            <Field>
                                <FieldLabel>Campaign Name *</FieldLabel>
                                <TextEdit @bind-Text="@Campaign.CampaignName" Placeholder="Enter campaign name">
                                    <ValidationError />
                                </TextEdit>
                            </Field>
                        </Validation>
                    </Column>
                </Row>

                <Row>
                    <Column ColumnSize="ColumnSize.Is12">
                        <Field>
                            <FieldLabel>Description</FieldLabel>
                            <MemoEdit @bind-Text="@Campaign.Description" Rows="3" Placeholder="Optional campaign description" />
                        </Field>
                    </Column>
                </Row>

                <!-- Reply Configuration -->
                <Row Class="mt-4">
                    <Column ColumnSize="ColumnSize.Is12">
                        <h6>Reply Configuration</h6>
                        <hr />
                    </Column>
                </Row>

                <Row>
                    <Column ColumnSize="ColumnSize.Is6">
                        <Field>
                            <Check TValue="bool" @bind-Checked="@Campaign.SendPublicReply">
                                Send Public Reply
                            </Check>
                            <FieldHelp>Reply publicly to comments on the post</FieldHelp>
                        </Field>
                        @if (Campaign.SendPublicReply)
                        {
                            <Validation>
                                <Field>
                                    <FieldLabel>Public Reply Message *</FieldLabel>
                                    <PersonalizationTagButtons OnTagSelected="@((tag) => InsertPersonalizationTag(tag, "publicReplyMessage"))" />
                                    <div class="position-relative">
                                        <MemoEdit @bind-Text="@Campaign.PublicReplyMessage" Rows="4" Placeholder="Enter your public reply message" Id="publicReplyMessage" />
                                        <div class="position-absolute bottom-0 end-0 p-2">
                                            <EmojiPicker OnEmojiSelected="@((emoji) => InsertEmoji(emoji, "publicReplyMessage"))" />
                                        </div>
                                    </div>
                                    @if (!string.IsNullOrEmpty(Campaign.PublicReplyMessage) && Campaign.PublicReplyMessage.Length > 1000)
                                    {
                                        <Alert Color="Color.Danger" Visible="true">
                                            <Icon Name="IconName.ExclamationTriangle" />
                                            Public reply message cannot exceed 1000 characters.
                                        </Alert>
                                    }
                                    <ValidationError />
                                </Field>
                            </Validation>
                        }
                    </Column>
                    <Column ColumnSize="ColumnSize.Is6">
                        <Field>
                            <Check TValue="bool" Checked="@Campaign.SendPrivateReply" CheckedChanged="@(async (bool value) => await OnPrivateReplyToggled(value))">
                                Send Private Message
                            </Check>
                            <FieldHelp>Send a private message to the commenter</FieldHelp>
                        </Field>
                        @if (Campaign.SendPrivateReply)
                        {
                            <!-- Reply Type Toggle -->
                            <Field>
                                <FieldLabel>Private Reply Type</FieldLabel>
                                <RadioGroup CheckedValue="@Campaign.PrivateReplyType" TValue="PrivateReplyType" CheckedValueChanged="@(async (PrivateReplyType value) => await OnPrivateReplyTypeChanged(value))">
                                    <Radio Value="PrivateReplyType.TextReply">Text Reply</Radio>
                                    <Radio Value="PrivateReplyType.CardReply">Card Reply</Radio>
                                </RadioGroup>
                                <FieldHelp>Choose between a simple text message or an interactive card</FieldHelp>
                            </Field>

                            @if (Campaign.PrivateReplyType == PrivateReplyType.TextReply)
                            {
                                <!-- Text Reply Editor -->
                                <Field>
                                    <FieldLabel>Private Message</FieldLabel>
                                    <PersonalizationTagButtons OnTagSelected="@((tag) => InsertPersonalizationTag(tag, "privateReplyMessage"))" />
                                    <div class="position-relative">
                                        <MemoEdit @bind-Text="@Campaign.PrivateReplyMessage" Rows="4" Placeholder="Enter your private message" Id="privateReplyMessage" />
                                        <div class="position-absolute bottom-0 end-0 p-2">
                                            <EmojiPicker Alignment="EmojiPicker.PickerAlignment.Right" OnEmojiSelected="@((emoji) => InsertEmoji(emoji, "privateReplyMessage"))" />
                                        </div>
                                    </div>
                                    @if (!Campaign.IncludePostLinkInPrivateReply)
                                    {
                                        if (!string.IsNullOrEmpty(Campaign.PrivateReplyMessage) && Campaign.PrivateReplyMessage.Length > 2000)
                                        {
                                            <Alert Color="Color.Danger" Visible="true">
                                                <Icon Name="IconName.ExclamationTriangle" />
                                                Private reply message cannot exceed 2000 characters.
                                            </Alert>
                                        }
                                    }
                                    else
                                    {
                                        var hasPicture = SelectedPost != null && !string.IsNullOrEmpty(SelectedPost.PostPictureUrl);
                                        if (hasPicture)
                                        {
                                            if (!string.IsNullOrEmpty(Campaign.PrivateReplyMessage) && Campaign.PrivateReplyMessage.Length > 500)
                                            {
                                                <Alert Color="Color.Danger" Visible="true">
                                                    <Icon Name="IconName.ExclamationTriangle" />
                                                    Private reply message cannot exceed 500 characters when using a template private reply.
                                                </Alert>
                                            }
                                            if (!string.IsNullOrEmpty(Campaign.PrivateReplyMessage) && Campaign.PrivateReplyMessage.Length > 50)
                                            {
                                                <Alert Color="Color.Warning" Visible="true">
                                                    <Icon Name="IconName.ExclamationTriangle" />
                                                    When including the post link and image, the private message will be used as the template title and will be truncated to 50 characters.
                                                </Alert>
                                            }
                                        }
                                        else
                                        {
                                            if (!string.IsNullOrEmpty(Campaign.PrivateReplyMessage) && Campaign.PrivateReplyMessage.Length > 500)
                                            {
                                                <Alert Color="Color.Danger" Visible="true">
                                                    <Icon Name="IconName.ExclamationTriangle" />
                                                    Private reply message cannot exceed 500 characters when using a template private reply.
                                                </Alert>
                                            }
                                        }
                                    }
                                    <FieldHelp>This message will be sent as a private message</FieldHelp>
                                </Field>
                            }
                            else
                            {
                                <!-- Card Reply Editor -->
                                <CardReplyEditor @bind-CardData="@Campaign.CardReplyData" OnValidationChanged="@OnCardReplyValidationChanged" />
                            }
                        }
                    </Column>
                </Row>

                <Row>
                    <Column ColumnSize="ColumnSize.Is6">
                        <Field>
                            <Check TValue="bool" @bind-Checked="@Campaign.SendLike">
                                Like User Comment
                            </Check>
                            <FieldHelp>Automatically like the user's comment</FieldHelp>
                        </Field>
                    </Column>
                </Row>

                <Row>
                    <Column ColumnSize="ColumnSize.Is6">
                        <Field>
                            <Check TValue="bool" @bind-Checked="@Campaign.IncludePostLinkInPrivateReply"
                                   Disabled="@(Campaign.SendPrivateReply && Campaign.PrivateReplyType == PrivateReplyType.CardReply)">
                                Include Post Link in Private Reply
                            </Check>
                            <FieldHelp>
                                @if (Campaign.SendPrivateReply && Campaign.PrivateReplyType == PrivateReplyType.CardReply)
                                {
                                    <span class="text-muted">Not available with Card Reply (post link is automatically included as default action)</span>
                                }
                                else
                                {
                                    <span>If enabled, the private reply will include a button linking to the post.</span>
                                }
                            </FieldHelp>
                        </Field>
                    </Column>
                </Row>

                @if (!Campaign.SendPublicReply && !Campaign.SendPrivateReply && !Campaign.SendLike)
                {
                    <Alert Color="Color.Warning" Visible="true">
                        <Icon Name="IconName.ExclamationTriangle" />
                        Please select at least one interaction type (Public Reply, Private Message, or Like Comment).
                    </Alert>
                }

                <!-- Campaign Settings -->
                <Row Class="mt-4">
                    <Column ColumnSize="ColumnSize.Is12">
                        <h6>Campaign Settings</h6>
                        <hr />
                    </Column>
                </Row>

                <Row>
                    <Column ColumnSize="ColumnSize.Is6">
                        <Field>
                            <FieldLabel>Start Date</FieldLabel>
                            <DateEdit @bind-Date="@Campaign.StartDate" />
                            <FieldHelp>Leave empty to start immediately</FieldHelp>
                        </Field>
                    </Column>
                    <Column ColumnSize="ColumnSize.Is6">
                        <Field>
                            <FieldLabel>End Date</FieldLabel>
                            <DateEdit @bind-Date="@Campaign.EndDate" />
                            <FieldHelp>Leave empty for no end date</FieldHelp>
                        </Field>
                    </Column>
                </Row>

                <Row>
                    <Column ColumnSize="ColumnSize.Is6">
                        <Field>
                            <FieldLabel>Max Replies Per User</FieldLabel>
                            <NumericEdit @bind-Value="@Campaign.MaxRepliesPerUser" Min="0" Max="10" />
                            <FieldHelp>Maximum number of auto-replies per user (0 = unlimited, 1-10 = limit)</FieldHelp>
                        </Field>
                    </Column>
                </Row>
            </Validations>

            <!-- Action Buttons -->
            <Row Class="mt-4">
                <Column ColumnSize="ColumnSize.Is12">
                    <div class="d-flex justify-content-end">
                        <Button Color="Color.Secondary" Class="me-2" Clicked="NavigateBack">
                            Cancel
                        </Button>
                        <Button Color="Color.Primary" Class="me-2" Clicked="SaveAsDraftAsync" Disabled="@(IsSaving || !CanSave)">
                            <Icon Name="IconName.Save" />
                            Save as Draft
                        </Button>
                        <Button Color="Color.Success" Clicked="SaveAndActivateAsync" Disabled="@(IsSaving || !CanSave)">
                            <Icon Name="IconName.Check" />
                            Create and Activate
                        </Button>
                    </div>
                </Column>
            </Row>
        }
        else
        {
            <Alert Color="Color.Danger" Visible="true">
                <Icon Name="IconName.ExclamationTriangle" />
                Post information not found. Please go back and select a valid post.
            </Alert>
        }
    </CardBody>
</Card>

@code {
    [Parameter]
    [SupplyParameterFromQuery]
    public string? PostId { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public string? PageId { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public string? PageName { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public string? PostContent { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public string? PostPermalinkUrl { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public string? PostPictureUrl { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public string? PostCreatedTime { get; set; }

    private bool IsLoading = false;
    private bool IsSaving = false;
    private SelectedPostInfo? SelectedPost;
    private CampaignFormModel Campaign = new();
    private Validations? ValidationsRef;
    private bool IsCardReplyValid = true;

    private bool CanSave =>
        !string.IsNullOrWhiteSpace(Campaign.CampaignName) &&
        (Campaign.SendPublicReply || Campaign.SendPrivateReply || Campaign.SendLike) &&
        (!Campaign.SendPublicReply || (!string.IsNullOrWhiteSpace(Campaign.PublicReplyMessage) && Campaign.PublicReplyMessage.Length <= 1000)) &&
        (!Campaign.SendPrivateReply || IsPrivateReplyValid());

    private bool IsPrivateReplyValid()
    {
        if (!Campaign.SendPrivateReply)
            return true;

        if (Campaign.PrivateReplyType == PrivateReplyType.CardReply)
        {
            return IsCardReplyValid && Campaign.CardReplyData.IsValid();
        }
        else
        {
            if (string.IsNullOrWhiteSpace(Campaign.PrivateReplyMessage))
                return false;
            if (!Campaign.IncludePostLinkInPrivateReply)
                return Campaign.PrivateReplyMessage.Length <= 2000;
            var hasPicture = SelectedPost != null && !string.IsNullOrEmpty(SelectedPost.PostPictureUrl);
            if (hasPicture)
                return Campaign.PrivateReplyMessage.Length <= 500;
            else
                return Campaign.PrivateReplyMessage.Length <= 500;
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            IsLoading = true;

            if (string.IsNullOrEmpty(PostId) || string.IsNullOrEmpty(PageId))
            {
                return;
            }

            SelectedPost = new SelectedPostInfo
            {
                FacebookPostId = PostId,
                FacebookPageId = PageId,
                PageName = PageName ?? "Unknown Page",
                PostContent = PostContent ?? string.Empty,
                PostPermalinkUrl = PostPermalinkUrl,
                PostPictureUrl = PostPictureUrl,
                PostCreatedTime = DateTime.TryParse(PostCreatedTime, out var createdTime) ? createdTime : DateTime.Now
            };

            Campaign = new CampaignFormModel
            {
                CampaignName = $"Auto-reply for {SelectedPost.PageName} post",
                SendPublicReply = true,
                SendPrivateReply = false,
                MaxRepliesPerUser = 0
            };
        }
        catch (Exception ex)
        {
            await UiMessage.Error($"Error loading data: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task SaveAsDraftAsync()
    {
        if (!await ValidateForm()) return;

        try
        {
            IsSaving = true;

            var createDto = CreateDto();
            await CampaignService.CreateFromPostAsync(createDto);
            await UiMessage.Success("Campaign created successfully!");

            NavigateBack();
        }
        catch (Exception ex)
        {
            await UiMessage.Error($"Error creating campaign: {ex.Message}");
        }
        finally
        {
            IsSaving = false;
        }
    }

    private async Task SaveAndActivateAsync()
    {
        if (!await ValidateForm()) return;

        try
        {
            IsSaving = true;

            var createDto = CreateDto();
            var campaign = await CampaignService.CreateFromPostAsync(createDto);
            campaign = await CampaignService.ActivateAsync(campaign.Id);
            await UiMessage.Success("Campaign created and activated successfully!");

            NavigationManager.NavigateTo("/campaigns");
        }
        catch (Exception ex)
        {
            await UiMessage.Error($"Error creating and activating campaign: {ex.Message}");
        }
        finally
        {
            IsSaving = false;
        }
    }

    private async Task<bool> ValidateForm()
    {
        if (ValidationsRef != null)
        {
            return await ValidationsRef.ValidateAll();
        }
        return false;
    }

    private CreateCampaignFromPostDto CreateDto()
    {
        return new CreateCampaignFromPostDto
        {
            FacebookPostId = SelectedPost!.FacebookPostId,
            FacebookPageId = SelectedPost.FacebookPageId,
            PageName = SelectedPost.PageName,
            PostContent = SelectedPost.PostContent,
            PostPermalinkUrl = SelectedPost.PostPermalinkUrl,
            PostPictureUrl = SelectedPost.PostPictureUrl,
            PostCreatedTime = SelectedPost.PostCreatedTime,
            CampaignName = Campaign.CampaignName,
            Description = Campaign.Description,
            PublicReplyMessage = Campaign.PublicReplyMessage,
            PrivateReplyMessage = Campaign.PrivateReplyMessage,
            SendPublicReply = Campaign.SendPublicReply,
            SendPrivateReply = Campaign.SendPrivateReply,
            SendLike = Campaign.SendLike,
            StartDate = Campaign.StartDate,
            EndDate = Campaign.EndDate,
            MaxRepliesPerUser = Campaign.MaxRepliesPerUser,
            IncludePostLinkInPrivateReply = Campaign.IncludePostLinkInPrivateReply,
            PrivateReplyType = Campaign.PrivateReplyType,
            CardReplyData = Campaign.PrivateReplyType == PrivateReplyType.CardReply ? Campaign.CardReplyData : null
        };
    }

    private async Task InsertEmoji(string emoji, string textareaId)
    {
        await JSRuntime.InvokeVoidAsync("insertTextAtCursor", textareaId, emoji);
        if (textareaId == "publicReplyMessage")
        {
            Campaign.PublicReplyMessage = (Campaign.PublicReplyMessage ?? "") + emoji;
        }
        else if (textareaId == "privateReplyMessage")
        {
            Campaign.PrivateReplyMessage = (Campaign.PrivateReplyMessage ?? "") + emoji;
        }
        StateHasChanged();
    }

    private async Task InsertPersonalizationTag(string tag, string textareaId)
    {
        await JSRuntime.InvokeVoidAsync("insertTextAtCursor", textareaId, tag);
        if (textareaId == "publicReplyMessage")
        {
            // Update the bound property to reflect the change
            var textarea = await JSRuntime.InvokeAsync<string>("getTextareaValue", textareaId);
            Campaign.PublicReplyMessage = textarea;
        }
        else if (textareaId == "privateReplyMessage")
        {
            // Update the bound property to reflect the change
            var textarea = await JSRuntime.InvokeAsync<string>("getTextareaValue", textareaId);
            Campaign.PrivateReplyMessage = textarea;
        }
        StateHasChanged();
    }

    private async Task OpenFacebookPostAsync(string permalinkUrl)
    {
        await JSRuntime.InvokeVoidAsync("open", permalinkUrl, "_blank");
    }

    private void NavigateBack()
    {
        NavigationManager.NavigateTo("/campaigns/select-post");
    }

    private async Task OnPrivateReplyToggled(bool isChecked)
    {
        Campaign.SendPrivateReply = isChecked;
        if (!isChecked)
        {
            // Reset private reply settings when disabled
            Campaign.PrivateReplyType = PrivateReplyType.TextReply;
            Campaign.PrivateReplyMessage = null;
            Campaign.CardReplyData = new CardReplyData();
            Campaign.IncludePostLinkInPrivateReply = false;
        }
        StateHasChanged();
    }

    private async Task OnPrivateReplyTypeChanged(PrivateReplyType newType)
    {
        Campaign.PrivateReplyType = newType;

        // When switching to Card Reply, disable "Include Post Link" option
        if (newType == PrivateReplyType.CardReply)
        {
            Campaign.IncludePostLinkInPrivateReply = false;
        }

        StateHasChanged();
    }

    private void OnCardReplyValidationChanged(bool isValid)
    {
        IsCardReplyValid = isValid;
        StateHasChanged();
    }

    public class SelectedPostInfo
    {
        public string FacebookPostId { get; set; } = string.Empty;
        public string FacebookPageId { get; set; } = string.Empty;
        public string PageName { get; set; } = string.Empty;
        public string PostContent { get; set; } = string.Empty;
        public string? PostPermalinkUrl { get; set; }
        public string? PostPictureUrl { get; set; }
        public DateTime PostCreatedTime { get; set; }
    }

    public class CampaignFormModel
    {
        [Required]
        [StringLength(256)]
        public string CampaignName { get; set; } = "";

        public string? Description { get; set; }

        [Required]
        public string PublicReplyMessage { get; set; } = "";

        public string? PrivateReplyMessage { get; set; }

        public bool SendPublicReply { get; set; } = true;

        public bool SendPrivateReply { get; set; } = false;

        public bool SendLike { get; set; } = false;

        public bool IncludePostLinkInPrivateReply { get; set; } = false;

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        [Range(0, 10)]
        public int MaxRepliesPerUser { get; set; } = 0;

        // New properties for Card Reply
        public PrivateReplyType PrivateReplyType { get; set; } = PrivateReplyType.TextReply;
        public CardReplyData CardReplyData { get; set; } = new();
    }
}
